/**
 * This is a sample billing configuration file. You should copy this file to `billing.config.ts` and then replace
 * the configuration with your own billing provider and products.
 */
import { BillingProviderSchema, createBillingSchema } from '@kit/billing';

// The billing provider to use. This should be set in the environment variables
// and should match the provider in the database. We also add it here so we can validate
// your configuration against the selected provider at build time.
const provider = BillingProviderSchema.parse(
  process.env.NEXT_PUBLIC_BILLING_PROVIDER,
);

export default createBillingSchema({
  // also update config.billing_provider in the DB to match the selected
  provider,
  // products configuration
  products: [
    {
      id: 'small_team',
      name: 'Small',
      badge: `Popular`,
      highlighted: true,
      description: 'Designed for founders and individual marketers managing their own brand. Create professional marketing content across multiple formats including text, images, and videos. Personalized to your brand voice with more content generation capabilities.',
      currency: 'USD',
      plans: [
        {
          name: 'Small',
          id: 'small-team-monthly',
          trialDays: 7,
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: process.env.NEXT_PUBLIC_SMALL_TEAM_PRICE_ID || '',
              name: 'Base',
              cost: 59.0,
              type: 'flat',
            },
            ],
        },
      ],
      features: [
        'Fair Usage Limits',
        '24x6 Chat support',
        'Individuals',
      ],
    },
    {
      id: 'medium_team',
      name: 'Medium',
      description: 'Built for seed-stage startups with small marketing teams. Includes collaboration features for seamless content creation and approval workflows. Supports multiple brands and products with expanded social media and video content options. Ideal for growing teams that need to scale content production efficiently.',
      currency: 'USD',
      badge: `Popular`,
      plans: [
        {
          name: 'Medium',
          id: 'medium-team-monthly',
          trialDays: 7,
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: process.env.NEXT_PUBLIC_MEDIUM_TEAM_PRICE_ID || '',
              name: 'Base',
              cost: 199.0,
              type: 'flat',
            },
          ],
        },
      ],
      features: [
        'Everything in Small',
        'Greater content generation capabilities',
        '24x7 Chat support',
        'Unlimited Team members and guests',
      ],
    },
    {
      id: 'large_team',
      name: 'Large',
      description: 'Comprehensive solution for Series A companies with larger marketing departments. Advanced collaboration tools, expanded content libraries, and priority AI processing. Support for multiple brands, products, and recruitment videos. Perfect for teams looking to maintain consistent messaging at scale across all marketing channels.',
      currency: 'USD',
      badge: `Popular`,
      plans: [
        {
          name: 'Large',
          id: 'large-team-monthly',
          trialDays: 7,
          paymentType: 'recurring',
          interval: 'month',
          lineItems: [
            {
              id: process.env.NEXT_PUBLIC_LARGE_TEAM_PRICE_ID || '',
              name: 'Base',
              cost: 499.0,
              type: 'flat',
            },
          ],
        },
      ],
      features: [
        'Everything in Medium',
        'Greater content generation capabilities',
        '24x7 Chat support',
        'Unlimited Team members and guests',
      ],
    },
  ],
});
