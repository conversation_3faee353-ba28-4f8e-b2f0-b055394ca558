'use client';

import { useMemo } from 'react';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from './use-zero';

export interface TrialStatus {
  hasActiveSubscription: boolean;
  isTrialing: boolean;
  trialExpired: boolean;
  trialExpiringSoon: boolean;
  canAccessApp: boolean;
  daysUntilExpiration: number | null;
  subscriptionExists: boolean;
  isLoading: boolean;
}

export interface TrialBadgeStatus {
  show: boolean;
  variant: 'success' | 'warning' | 'destructive';
  message: string;
  isLoading: boolean;
}

export function useTrialStatus(): TrialStatus {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  
  const [subscriptions, result] = useZeroQuery(
    zero.query.subscriptions.where('account_id', '=', workspace.account.id),
    { ttl: '5m' }
  );

  return useMemo(() => {
    const isLoading = result.type === 'unknown';
    
    if (isLoading) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false,
        trialExpiringSoon: false,
        canAccessApp: true,
        daysUntilExpiration: null,
        subscriptionExists: false,
        isLoading: true,
      };
    }

    const subscription = subscriptions?.find(sub => sub.active) || subscriptions?.[0];

    // NEW USER: No subscription exists at all
    if (!subscription) {
      return {
        hasActiveSubscription: false,
        isTrialing: false,
        trialExpired: false, // Important: NOT expired, just no subscription yet
        trialExpiringSoon: false,
        canAccessApp: true, // Allow access for new users
        daysUntilExpiration: null,
        subscriptionExists: false,
        isLoading: false,
      };
    }

    const isTrialing = subscription.status === 'trialing';
    const hasActiveSubscription = subscription.status === 'active' || isTrialing;
    
    let trialExpired = false;
    let trialExpiringSoon = false;
    let daysUntilExpiration: number | null = null;

    if (subscription.trial_ends_at) {
      const trialEndsAt = new Date(subscription.trial_ends_at);
      const now = new Date();
      const timeDiff = trialEndsAt.getTime() - now.getTime();
      daysUntilExpiration = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      
      trialExpired = timeDiff <= 0;
      trialExpiringSoon = timeDiff > 0 && daysUntilExpiration <= 3;
    }

    return {
      hasActiveSubscription,
      isTrialing,
      trialExpired,
      trialExpiringSoon,
      canAccessApp: hasActiveSubscription && (!isTrialing || !trialExpired),
      daysUntilExpiration,
      subscriptionExists: true,
      isLoading: false,
    };
  }, [subscriptions, result.type]);
}

export function useTrialBadge(): TrialBadgeStatus {
  const {
    isTrialing,
    trialExpired,
    trialExpiringSoon,
    daysUntilExpiration,
    hasActiveSubscription,
    subscriptionExists,
    isLoading
  } = useTrialStatus();

  return useMemo(() => {
    if (isLoading) {
      return { show: false, variant: 'success', message: '', isLoading: true };
    }

    // NEW USER: No subscription exists at all - hide badge completely
    if (!subscriptionExists) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // ACTIVE SUBSCRIPTION: User has active paid subscription - hide badge
    if (hasActiveSubscription && !isTrialing) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // EXPIRED TRIAL: User had a trial subscription but it expired
    if (isTrialing && trialExpired) {
      return { show: true, variant: 'destructive', message: 'Trial expired', isLoading: false };
    }

    // TRIAL EXPIRING SOON: Trial exists and expiring within 3 days
    if (isTrialing && trialExpiringSoon && daysUntilExpiration !== null) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'warning',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    // ACTIVE TRIAL: Trial exists with more than 3 days remaining
    if (isTrialing && daysUntilExpiration !== null && daysUntilExpiration > 3) {
      const days = daysUntilExpiration === 1 ? 'day' : 'days';
      return {
        show: true,
        variant: 'success',
        message: `Trial expires in ${daysUntilExpiration} ${days}`,
        isLoading: false,
      };
    }

    // INACTIVE SUBSCRIPTION: User had subscription but it's now inactive/canceled
    // Hide badge for canceled/past_due subscriptions
    if (!hasActiveSubscription && subscriptionExists) {
      return { show: false, variant: 'success', message: '', isLoading: false };
    }

    // DEFAULT: Hide badge
    return { show: false, variant: 'success', message: '', isLoading: false };
  }, [isTrialing, trialExpired, trialExpiringSoon, daysUntilExpiration, hasActiveSubscription, subscriptionExists, isLoading]);
}
