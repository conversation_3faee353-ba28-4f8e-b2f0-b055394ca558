import React from "react";
import { CompanyContent } from "~/types/company-content";
import { Card } from "@kit/ui/card";
import { useTheme } from "next-themes";
import GeneralContentEditor from "./general-content-editor";
import { SocialProfile } from "../../index";
import { useZero } from "~/hooks/use-zero";
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useParams } from "next/navigation";
import { useImageContent } from "../../context/ContentStudioContext";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

// Component that contains the Blog Post Preview content
export function BlogPostPreview({ selectedProfile }: { selectedProfile?: SocialProfile | null }) {
  const { resolvedTheme } = useTheme();
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const { setSelectedEditorImage, setSelectedImageOption } = useImageContent();

  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", workspace.account.id as string),
    {
      ttl: '10m'
    }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];

  // Utility function to detect if URL is a video
  const isVideoUrl = (url: string): boolean => {
    return url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') || url.includes('.webm');
  };

  // Function to handle image click for editing
  const handleImageClick = (imageUrl: string) => {
    setSelectedEditorImage(imageUrl);
    setSelectedImageOption('selected');
  };

  // Theme-based styling
  const isDark = resolvedTheme === 'dark';
  const cardBg = isDark ? 'bg-gray-900' : 'bg-white';
  const textColor = isDark ? 'text-white' : 'text-gray-900';
  const borderColor = isDark ? 'border-gray-700' : 'border-gray-200';

  return (
    <div className="mx-auto max-w-4xl p-6">
      <Card className={`w-full ${cardBg} ${textColor} shadow-sm border ${borderColor} rounded-lg overflow-hidden`}>
        <div className="p-8">
          {/* Blog post content */}
          <article className="prose prose-lg max-w-none">
            <GeneralContentEditor companyContent={selectedCompanyContent as unknown as CompanyContent} />
          </article>
          
          {/* Media section - images and videos */}
          {/* @ts-expect-error - image_urls is not typed */}
          {selectedCompanyContent?.image_urls && selectedCompanyContent.image_urls.length > 0 && (
            <div className="mt-8 space-y-4">
              {/* @ts-expect-error - image_urls is not typed */}
              {selectedCompanyContent.image_urls.map((url, index) => (
                <div key={index} className={`rounded-lg overflow-hidden border ${borderColor}`}>
                  {isVideoUrl(url) ? (
                    <video 
                      src={url} 
                      className="w-full h-auto object-cover"
                      controls
                      loop
                      muted
                    />
                  ) : (
                    <img 
                      src={url} 
                      alt={`Blog post image ${index + 1}`}
                      className="w-full h-auto object-cover cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => handleImageClick(url)}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
